// Mixin to handle attachment display states and error handling
export default {
  data() {
    return {
      imageLoadRetryCount: 0,
      maxRetryCount: 3,
      retryDelay: 1000, // 1 second
    };
  },
  
  computed: {
    isAttachmentLoading() {
      return this.attachment?.status === 'uploading' || this.attachment?.status === 'in_progress';
    },
    
    hasValidDataUrl() {
      return this.attachment?.data_url && this.attachment.data_url !== '';
    },
    
    shouldShowLoadingState() {
      return this.isAttachmentLoading && this.hasValidDataUrl;
    },
    
    shouldShowErrorState() {
      return !this.isAttachmentLoading && (!this.hasValidDataUrl || this.isImageError);
    },
  },
  
  methods: {
    handleImageError() {
      if (this.imageLoadRetryCount < this.maxRetryCount && this.hasValidDataUrl) {
        // Retry loading the image after a delay
        this.imageLoadRetryCount++;
        setTimeout(() => {
          // Force re-render by updating the src
          const img = this.$el.querySelector('img');
          if (img) {
            const originalSrc = img.src;
            img.src = '';
            setTimeout(() => {
              img.src = originalSrc;
            }, 100);
          }
        }, this.retryDelay * this.imageLoadRetryCount);
      } else {
        this.isImageError = true;
        this.$emit('error');
      }
    },
    
    resetImageState() {
      this.isImageError = false;
      this.imageLoadRetryCount = 0;
    },
  },
  
  watch: {
    'attachment.data_url'() {
      // Reset error state when data_url changes
      this.resetImageState();
    },
    
    'attachment.status'(newStatus) {
      // Reset error state when status changes from uploading to sent
      if (newStatus === 'sent') {
        this.resetImageState();
      }
    },
  },
};
